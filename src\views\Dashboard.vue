<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>仪表盘</h1>
      <p>欢迎使用评估分析系统，这里是系统概览</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon primary">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ overviewStats.totalEvaluations || 0 }}</div>
            <div class="stat-label">总评估数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ overviewStats.totalParticipants || 0 }}</div>
            <div class="stat-label">参与人数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ overviewStats.averageScore || 0 }}</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ overviewStats.completionRate || 0 }}%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <div class="chart-card">
          <div class="card-header">
            <h3>评估趋势</h3>
          </div>
          <div class="chart-container">
            <v-chart 
              v-if="trendChartOption" 
              :option="trendChartOption" 
              style="height: 300px;"
            />
          </div>
        </div>
      </el-col>
      
      <el-col :span="12">
        <div class="chart-card">
          <div class="card-header">
            <h3>评估类型分布</h3>
          </div>
          <div class="chart-container">
            <v-chart 
              v-if="pieChartOption" 
              :option="pieChartOption" 
              style="height: 300px;"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <div class="recent-activities">
      <div class="card-header">
        <h3>最近活动</h3>
        <el-button type="primary" size="small" @click="$router.push('/evaluation')">
          查看全部
        </el-button>
      </div>
      
      <el-table :data="recentEvaluations" style="width: 100%">
        <el-table-column prop="title" label="评估标题" />
        <el-table-column prop="type" label="类型">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participants" label="参与人数" />
        <el-table-column prop="completedCount" label="已完成" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { Document, User, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'
import { useAnalysisStore } from '@/stores/analysis'
import { useEvaluationStore } from '@/stores/evaluation'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const analysisStore = useAnalysisStore()
const evaluationStore = useEvaluationStore()

const overviewStats = computed(() => analysisStore.overviewStats)
const recentEvaluations = computed(() => evaluationStore.evaluations.slice(0, 5))

// 趋势图配置
const trendChartOption = computed(() => {
  const trendData = analysisStore.trendData
  if (!trendData.months.length) return null
  
  return {
    tooltip: { trigger: 'axis' },
    legend: { data: ['评估数量', '参与人数'] },
    xAxis: { type: 'category', data: trendData.months },
    yAxis: { type: 'value' },
    series: [
      {
        name: '评估数量',
        type: 'line',
        data: trendData.evaluations,
        smooth: true
      },
      {
        name: '参与人数',
        type: 'bar',
        data: trendData.participants
      }
    ]
  }
})

// 饼图配置
const pieChartOption = computed(() => {
  const categoryData = analysisStore.categoryData
  if (!categoryData.length) return null
  
  return {
    tooltip: { trigger: 'item' },
    legend: { orient: 'vertical', left: 'left' },
    series: [{
      type: 'pie',
      radius: '50%',
      data: categoryData.map(item => ({
        name: item.name,
        value: item.count
      })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
})

// 工具方法
const getTypeLabel = (type) => {
  const labels = {
    performance: '绩效评估',
    satisfaction: '满意度调查',
    training: '培训评估'
  }
  return labels[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    performance: 'primary',
    satisfaction: 'success',
    training: 'warning'
  }
  return types[type] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    active: '进行中',
    completed: '已完成',
    draft: '草稿'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    completed: 'info',
    draft: 'warning'
  }
  return types[status] || 'info'
}

onMounted(async () => {
  await Promise.all([
    analysisStore.fetchAnalysisData(),
    evaluationStore.fetchEvaluations()
  ])
})
</script>

<style lang="less" scoped>
.dashboard {
  .page-header {
    margin-bottom: @spacing-xl;
    
    h1 {
      margin: 0 0 @spacing-sm 0;
      color: @text-color-primary;
    }
    
    p {
      margin: 0;
      color: @text-color-secondary;
    }
  }
  
  .stats-row {
    margin-bottom: @spacing-xl;
    
    .stat-card {
      background: #fff;
      border-radius: @border-radius-base;
      box-shadow: @box-shadow-base;
      padding: @spacing-lg;
      display: flex;
      align-items: center;
      gap: @spacing-md;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        .flex-center();
        font-size: 24px;
        color: #fff;
        
        &.primary { background-color: @primary-color; }
        &.success { background-color: @success-color; }
        &.warning { background-color: @warning-color; }
        &.info { background-color: @info-color; }
      }
      
      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: @text-color-primary;
          line-height: 1;
        }
        
        .stat-label {
          font-size: @font-size-sm;
          color: @text-color-secondary;
          margin-top: @spacing-xs;
        }
      }
    }
  }
  
  .charts-row {
    margin-bottom: @spacing-xl;
    
    .chart-card {
      background: #fff;
      border-radius: @border-radius-base;
      box-shadow: @box-shadow-base;
      padding: @spacing-lg;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @spacing-md;
        padding-bottom: @spacing-md;
        border-bottom: 1px solid @border-color-lighter;
        
        h3 {
          margin: 0;
          color: @text-color-primary;
        }
      }
      
      .chart-container {
        height: 300px;
      }
    }
  }
  
  .recent-activities {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-md;
      
      h3 {
        margin: 0;
        color: @text-color-primary;
      }
    }
  }
}
</style>
