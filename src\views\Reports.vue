<template>
  <div class="reports-page">
    <div class="page-header">
      <h1>报告中心</h1>
      <el-button type="primary" @click="generateReport">
        <el-icon><Document /></el-icon>
        生成报告
      </el-button>
    </div>
    
    <!-- 报告筛选 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-select v-model="reportType" placeholder="报告类型" clearable>
            <el-option label="全部" value="" />
            <el-option label="综合分析报告" value="comprehensive" />
            <el-option label="评估统计报告" value="statistics" />
            <el-option label="趋势分析报告" value="trend" />
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="searchReports">搜索</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 报告列表 -->
    <div class="reports-list">
      <el-table :data="reportsList" style="width: 100%">
        <el-table-column prop="title" label="报告标题" min-width="200" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag>{{ getReportTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="generateTime" label="生成时间" width="150" />
        <el-table-column prop="size" label="文件大小" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button size="small" @click="previewReport(row)">预览</el-button>
            <el-button size="small" type="primary" @click="downloadReport(row)">下载</el-button>
            <el-button size="small" type="danger" @click="deleteReport(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 生成报告对话框 -->
    <el-dialog v-model="showGenerateDialog" title="生成报告" width="500px">
      <el-form :model="reportForm" label-width="100px">
        <el-form-item label="报告标题">
          <el-input v-model="reportForm.title" placeholder="请输入报告标题" />
        </el-form-item>
        <el-form-item label="报告类型">
          <el-select v-model="reportForm.type" placeholder="请选择报告类型">
            <el-option label="综合分析报告" value="comprehensive" />
            <el-option label="评估统计报告" value="statistics" />
            <el-option label="趋势分析报告" value="trend" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="reportForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="输出格式">
          <el-checkbox-group v-model="reportForm.formats">
            <el-checkbox label="pdf">PDF</el-checkbox>
            <el-checkbox label="excel">Excel</el-checkbox>
            <el-checkbox label="word">Word</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showGenerateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitGenerateReport" :loading="generating">
          生成报告
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

// 响应式数据
const reportType = ref('')
const dateRange = ref([])
const showGenerateDialog = ref(false)
const generating = ref(false)

const reportForm = ref({
  title: '',
  type: '',
  dateRange: [],
  formats: ['pdf']
})

// 模拟报告数据
const reportsList = ref([
  {
    id: 1,
    title: '2024年第一季度综合分析报告',
    type: 'comprehensive',
    generateTime: '2024-01-25 14:30:00',
    size: '2.5MB',
    status: 'completed'
  },
  {
    id: 2,
    title: '员工绩效评估统计报告',
    type: 'statistics',
    generateTime: '2024-01-20 09:15:00',
    size: '1.8MB',
    status: 'completed'
  },
  {
    id: 3,
    title: '客户满意度趋势分析',
    type: 'trend',
    generateTime: '2024-01-18 16:45:00',
    size: '3.2MB',
    status: 'generating'
  }
])

// 方法
const generateReport = () => {
  showGenerateDialog.value = true
}

const searchReports = () => {
  // 实现搜索逻辑
  ElMessage.info('搜索功能待实现')
}

const previewReport = (report) => {
  ElMessage.info(`预览报告: ${report.title}`)
}

const downloadReport = (report) => {
  ElMessage.success(`下载报告: ${report.title}`)
}

const deleteReport = async (report) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除报告"${report.title}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    const index = reportsList.value.findIndex(item => item.id === report.id)
    if (index !== -1) {
      reportsList.value.splice(index, 1)
    }
    
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const submitGenerateReport = async () => {
  if (!reportForm.value.title || !reportForm.value.type) {
    ElMessage.warning('请填写完整信息')
    return
  }
  
  generating.value = true
  
  try {
    // 模拟生成报告
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const newReport = {
      id: Date.now(),
      title: reportForm.value.title,
      type: reportForm.value.type,
      generateTime: new Date().toLocaleString(),
      size: '1.5MB',
      status: 'completed'
    }
    
    reportsList.value.unshift(newReport)
    
    ElMessage.success('报告生成成功')
    showGenerateDialog.value = false
    
    // 重置表单
    reportForm.value = {
      title: '',
      type: '',
      dateRange: [],
      formats: ['pdf']
    }
  } catch (error) {
    ElMessage.error('报告生成失败')
  } finally {
    generating.value = false
  }
}

// 工具方法
const getReportTypeLabel = (type) => {
  const labels = {
    comprehensive: '综合分析',
    statistics: '统计报告',
    trend: '趋势分析'
  }
  return labels[type] || type
}

const getStatusLabel = (status) => {
  const labels = {
    completed: '已完成',
    generating: '生成中',
    failed: '失败'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    completed: 'success',
    generating: 'warning',
    failed: 'danger'
  }
  return types[status] || 'info'
}

onMounted(() => {
  // 初始化数据
})
</script>

<style lang="less" scoped>
.reports-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-xl;
    
    h1 {
      margin: 0;
      color: @text-color-primary;
    }
  }
  
  .filter-section {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    margin-bottom: @spacing-lg;
  }

  .reports-list {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
  }
}
</style>
