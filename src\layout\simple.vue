<template>
  <div class="simple-layout">
    <div class="header">
      <h1>评估分析系统</h1>
      <nav class="nav">
        <router-link to="/dashboard" class="nav-link">仪表盘</router-link>
        <router-link to="/evaluation" class="nav-link">评估管理</router-link>
        <router-link to="/analysis" class="nav-link">数据分析</router-link>
        <router-link to="/reports" class="nav-link">报告中心</router-link>
        <router-link to="/settings" class="nav-link">系统设置</router-link>
      </nav>
    </div>
    
    <div class="content">
      <router-view />
    </div>
  </div>
</template>

<script setup>
console.log('Simple layout loaded')
</script>

<style lang="less" scoped>
.simple-layout {
  min-height: 100vh;
  background-color: #f5f7fa;
  
  .header {
    background: #fff;
    padding: 16px 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h1 {
      margin: 0;
      color: #303133;
      font-size: 20px;
    }
    
    .nav {
      display: flex;
      gap: 24px;
      
      .nav-link {
        color: #606266;
        text-decoration: none;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f7fa;
          color: #409EFF;
        }
        
        &.router-link-active {
          background-color: #409EFF;
          color: #fff;
        }
      }
    }
  }
  
  .content {
    padding: 24px;
  }
}
</style>
