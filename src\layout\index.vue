<template>
  <div class="layout-container">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar">
        <div class="logo">
          <h2 v-if="!isCollapse">评估分析系统</h2>
          <h2 v-else>评估</h2>
        </div>
        
        <el-menu
          :default-active="$route.path"
          :collapse="isCollapse"
          :unique-opened="true"
          router
          class="sidebar-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Dashboard /></el-icon>
            <template #title>仪表盘</template>
          </el-menu-item>
          
          <el-menu-item index="/evaluation">
            <el-icon><Document /></el-icon>
            <template #title>评估管理</template>
          </el-menu-item>
          
          <el-menu-item index="/analysis">
            <el-icon><DataAnalysis /></el-icon>
            <template #title>数据分析</template>
          </el-menu-item>
          
          <el-menu-item index="/reports">
            <el-icon><Document /></el-icon>
            <template #title>报告中心</template>
          </el-menu-item>
          
          <el-menu-item index="/settings">
            <el-icon><Setting /></el-icon>
            <template #title>系统设置</template>
          </el-menu-item>
        </el-menu>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-container>
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-button 
              :icon="isCollapse ? Expand : Fold" 
              @click="toggleCollapse"
              text
            />
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentPageTitle }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          
          <div class="header-right">
            <el-dropdown>
              <span class="user-info">
                <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" />
                <span class="username">管理员</span>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item>个人中心</el-dropdown-item>
                  <el-dropdown-item>修改密码</el-dropdown-item>
                  <el-dropdown-item divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 主要内容 -->
        <el-main class="main-content">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Dashboard, Document, DataAnalysis, Setting, Expand, Fold } from '@element-plus/icons-vue'

const route = useRoute()
const isCollapse = ref(false)

const currentPageTitle = computed(() => {
  const titleMap = {
    '/dashboard': '仪表盘',
    '/evaluation': '评估管理',
    '/analysis': '数据分析',
    '/reports': '报告中心',
    '/settings': '系统设置'
  }
  return titleMap[route.path] || '未知页面'
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style lang="less" scoped>
.layout-container {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
  
  .sidebar {
    background-color: #304156;
    transition: width 0.3s;
    
    .logo {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: 18px;
      font-weight: bold;
      border-bottom: 1px solid #434a50;
      
      h2 {
        margin: 0;
        font-size: 16px;
      }
    }
    
    .sidebar-menu {
      border: none;
      background-color: #304156;
      
      :deep(.el-menu-item) {
        color: #bfcbd9;
        
        &:hover {
          background-color: #263445;
          color: #fff;
        }
        
        &.is-active {
          background-color: @primary-color;
          color: #fff;
        }
      }
    }
  }
  
  .header {
    background-color: #fff;
    border-bottom: 1px solid @border-color-light;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 @spacing-lg;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: @spacing-md;
    }
    
    .header-right {
      .user-info {
        display: flex;
        align-items: center;
        gap: @spacing-sm;
        cursor: pointer;
        
        .username {
          color: @text-color-primary;
          font-size: @font-size-sm;
        }
      }
    }
  }
  
  .main-content {
    background-color: @background-color-base;
    padding: @spacing-lg;
  }
}
</style>
