@import './variables.less';
@import './mixins.less';

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  font-size: @font-size-base;
  color: @text-color-primary;
  background-color: @background-color-base;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: @background-color-light;
}

::-webkit-scrollbar-thumb {
  background: @border-color-base;
  border-radius: 3px;
  
  &:hover {
    background: @text-color-placeholder;
  }
}

// 通用工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-xs { margin-top: @spacing-xs; }
.mt-sm { margin-top: @spacing-sm; }
.mt-md { margin-top: @spacing-md; }
.mt-lg { margin-top: @spacing-lg; }
.mt-xl { margin-top: @spacing-xl; }

.mb-xs { margin-bottom: @spacing-xs; }
.mb-sm { margin-bottom: @spacing-sm; }
.mb-md { margin-bottom: @spacing-md; }
.mb-lg { margin-bottom: @spacing-lg; }
.mb-xl { margin-bottom: @spacing-xl; }

.pt-xs { padding-top: @spacing-xs; }
.pt-sm { padding-top: @spacing-sm; }
.pt-md { padding-top: @spacing-md; }
.pt-lg { padding-top: @spacing-lg; }
.pt-xl { padding-top: @spacing-xl; }

.pb-xs { padding-bottom: @spacing-xs; }
.pb-sm { padding-bottom: @spacing-sm; }
.pb-md { padding-bottom: @spacing-md; }
.pb-lg { padding-bottom: @spacing-lg; }
.pb-xl { padding-bottom: @spacing-xl; }

// 卡片容器
.card {
  .card-style();
}

// 卡片样式类
.card-style {
  background: #fff;
  border-radius: @border-radius-base;
  box-shadow: @box-shadow-base;
  padding: @spacing-lg;
}

// 页面容器
.page-container {
  padding: @spacing-lg;
  min-height: 100vh;
}

.content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
}
