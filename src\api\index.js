import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    const { data } = response
    
    // 统一处理响应格式
    if (data.code === 200) {
      return data.data
    } else {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
  },
  error => {
    // 处理HTTP错误
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络错误，请检查网络连接')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 评估相关API
export const evaluationApi = {
  // 获取评估列表
  getList: (params) => api.get('/evaluations', { params }),
  
  // 获取评估详情
  getDetail: (id) => api.get(`/evaluations/${id}`),
  
  // 创建评估
  create: (data) => api.post('/evaluations', data),
  
  // 更新评估
  update: (id, data) => api.put(`/evaluations/${id}`, data),
  
  // 删除评估
  delete: (id) => api.delete(`/evaluations/${id}`),
  
  // 发布评估
  publish: (id) => api.post(`/evaluations/${id}/publish`),
  
  // 获取评估统计
  getStats: (id) => api.get(`/evaluations/${id}/stats`)
}

// 分析相关API
export const analysisApi = {
  // 获取概览数据
  getOverview: (params) => api.get('/analysis/overview', { params }),
  
  // 获取趋势数据
  getTrends: (params) => api.get('/analysis/trends', { params }),
  
  // 获取分类统计
  getCategories: (params) => api.get('/analysis/categories', { params }),
  
  // 获取评分分布
  getScoreDistribution: (params) => api.get('/analysis/score-distribution', { params }),
  
  // 导出分析报告
  exportReport: (params) => api.post('/analysis/export', params, {
    responseType: 'blob'
  })
}

// 报告相关API
export const reportApi = {
  // 获取报告列表
  getList: (params) => api.get('/reports', { params }),
  
  // 生成报告
  generate: (data) => api.post('/reports/generate', data),
  
  // 下载报告
  download: (id) => api.get(`/reports/${id}/download`, {
    responseType: 'blob'
  }),
  
  // 删除报告
  delete: (id) => api.delete(`/reports/${id}`),
  
  // 获取报告预览
  preview: (id) => api.get(`/reports/${id}/preview`)
}

// 用户相关API
export const userApi = {
  // 登录
  login: (data) => api.post('/auth/login', data),
  
  // 登出
  logout: () => api.post('/auth/logout'),
  
  // 获取用户信息
  getUserInfo: () => api.get('/user/info'),
  
  // 更新用户信息
  updateUserInfo: (data) => api.put('/user/info', data),
  
  // 修改密码
  changePassword: (data) => api.put('/user/password', data)
}

// 系统设置相关API
export const settingsApi = {
  // 获取系统设置
  getSettings: () => api.get('/settings'),
  
  // 更新系统设置
  updateSettings: (data) => api.put('/settings', data),
  
  // 创建备份
  createBackup: () => api.post('/settings/backup'),
  
  // 获取备份列表
  getBackups: () => api.get('/settings/backups'),
  
  // 恢复备份
  restoreBackup: (id) => api.post(`/settings/backups/${id}/restore`)
}

export default api
