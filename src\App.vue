<template>
  <div id="app">
    <h1>评估分析系统</h1>
    <p>欢迎使用评估分析系统！</p>
    <div class="features">
      <div class="feature-card">
        <h3>仪表盘</h3>
        <p>查看系统概览和统计数据</p>
      </div>
      <div class="feature-card">
        <h3>评估管理</h3>
        <p>创建和管理各类评估</p>
      </div>
      <div class="feature-card">
        <h3>数据分析</h3>
        <p>深入分析评估数据</p>
      </div>
      <div class="feature-card">
        <h3>报告中心</h3>
        <p>生成和下载分析报告</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 应用根组件
</script>

<style scoped>
#app {
  width: 100%;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubunt<PERSON>, Can<PERSON>ell, sans-serif;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

p {
  text-align: center;
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.feature-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #fff;
}

.feature-card p {
  font-size: 1rem;
  opacity: 0.8;
  margin: 0;
}
</style>
