<template>
  <div class="settings-page">
    <div class="page-header">
      <h1>系统设置</h1>
    </div>
    
    <el-row :gutter="20">
      <!-- 左侧菜单 -->
      <el-col :span="6">
        <div class="settings-menu">
          <el-menu :default-active="activeTab" @select="handleMenuSelect">
            <el-menu-item index="basic">
              <el-icon><Setting /></el-icon>
              <span>基础设置</span>
            </el-menu-item>
            <el-menu-item index="evaluation">
              <el-icon><Document /></el-icon>
              <span>评估设置</span>
            </el-menu-item>
            <el-menu-item index="notification">
              <el-icon><Bell /></el-icon>
              <span>通知设置</span>
            </el-menu-item>
            <el-menu-item index="security">
              <el-icon><Lock /></el-icon>
              <span>安全设置</span>
            </el-menu-item>
            <el-menu-item index="backup">
              <el-icon><FolderOpened /></el-icon>
              <span>备份设置</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-col>
      
      <!-- 右侧内容 -->
      <el-col :span="18">
        <div class="settings-content">
          <!-- 基础设置 -->
          <div v-show="activeTab === 'basic'" class="setting-panel">
            <h3>基础设置</h3>
            <el-form :model="basicSettings" label-width="120px">
              <el-form-item label="系统名称">
                <el-input v-model="basicSettings.systemName" />
              </el-form-item>
              <el-form-item label="系统描述">
                <el-input v-model="basicSettings.systemDesc" type="textarea" :rows="3" />
              </el-form-item>
              <el-form-item label="时区设置">
                <el-select v-model="basicSettings.timezone">
                  <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                  <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                  <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                </el-select>
              </el-form-item>
              <el-form-item label="语言设置">
                <el-select v-model="basicSettings.language">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 评估设置 -->
          <div v-show="activeTab === 'evaluation'" class="setting-panel">
            <h3>评估设置</h3>
            <el-form :model="evaluationSettings" label-width="120px">
              <el-form-item label="默认评估类型">
                <el-select v-model="evaluationSettings.defaultType">
                  <el-option label="绩效评估" value="performance" />
                  <el-option label="满意度调查" value="satisfaction" />
                  <el-option label="培训评估" value="training" />
                </el-select>
              </el-form-item>
              <el-form-item label="评估有效期">
                <el-input-number v-model="evaluationSettings.validDays" :min="1" :max="365" />
                <span style="margin-left: 8px;">天</span>
              </el-form-item>
              <el-form-item label="自动提醒">
                <el-switch v-model="evaluationSettings.autoReminder" />
              </el-form-item>
              <el-form-item label="提醒间隔">
                <el-input-number v-model="evaluationSettings.reminderInterval" :min="1" :max="30" />
                <span style="margin-left: 8px;">天</span>
              </el-form-item>
              <el-form-item label="匿名评估">
                <el-switch v-model="evaluationSettings.allowAnonymous" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveEvaluationSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 通知设置 -->
          <div v-show="activeTab === 'notification'" class="setting-panel">
            <h3>通知设置</h3>
            <el-form :model="notificationSettings" label-width="120px">
              <el-form-item label="邮件通知">
                <el-switch v-model="notificationSettings.emailEnabled" />
              </el-form-item>
              <el-form-item label="短信通知">
                <el-switch v-model="notificationSettings.smsEnabled" />
              </el-form-item>
              <el-form-item label="系统通知">
                <el-switch v-model="notificationSettings.systemEnabled" />
              </el-form-item>
              <el-form-item label="通知频率">
                <el-radio-group v-model="notificationSettings.frequency">
                  <el-radio label="immediate">立即</el-radio>
                  <el-radio label="daily">每日汇总</el-radio>
                  <el-radio label="weekly">每周汇总</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 安全设置 -->
          <div v-show="activeTab === 'security'" class="setting-panel">
            <h3>安全设置</h3>
            <el-form :model="securitySettings" label-width="120px">
              <el-form-item label="密码策略">
                <el-checkbox-group v-model="securitySettings.passwordPolicy">
                  <el-checkbox label="minLength">最少8位字符</el-checkbox>
                  <el-checkbox label="requireNumber">包含数字</el-checkbox>
                  <el-checkbox label="requireSymbol">包含特殊字符</el-checkbox>
                  <el-checkbox label="requireUppercase">包含大写字母</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item label="会话超时">
                <el-input-number v-model="securitySettings.sessionTimeout" :min="30" :max="1440" />
                <span style="margin-left: 8px;">分钟</span>
              </el-form-item>
              <el-form-item label="登录失败限制">
                <el-input-number v-model="securitySettings.maxLoginAttempts" :min="3" :max="10" />
                <span style="margin-left: 8px;">次</span>
              </el-form-item>
              <el-form-item label="双因子认证">
                <el-switch v-model="securitySettings.twoFactorAuth" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveSecuritySettings">保存设置</el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 备份设置 -->
          <div v-show="activeTab === 'backup'" class="setting-panel">
            <h3>备份设置</h3>
            <el-form :model="backupSettings" label-width="120px">
              <el-form-item label="自动备份">
                <el-switch v-model="backupSettings.autoBackup" />
              </el-form-item>
              <el-form-item label="备份频率">
                <el-select v-model="backupSettings.frequency">
                  <el-option label="每日" value="daily" />
                  <el-option label="每周" value="weekly" />
                  <el-option label="每月" value="monthly" />
                </el-select>
              </el-form-item>
              <el-form-item label="保留天数">
                <el-input-number v-model="backupSettings.retentionDays" :min="7" :max="365" />
                <span style="margin-left: 8px;">天</span>
              </el-form-item>
              <el-form-item label="备份路径">
                <el-input v-model="backupSettings.backupPath" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveBackupSettings">保存设置</el-button>
                <el-button @click="createBackup" :loading="backupLoading">立即备份</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Setting, Document, Bell, Lock, FolderOpened } from '@element-plus/icons-vue'

// 响应式数据
const activeTab = ref('basic')
const backupLoading = ref(false)

// 设置数据
const basicSettings = ref({
  systemName: '评估分析系统',
  systemDesc: '企业级评估分析管理平台',
  timezone: 'Asia/Shanghai',
  language: 'zh-CN'
})

const evaluationSettings = ref({
  defaultType: 'performance',
  validDays: 30,
  autoReminder: true,
  reminderInterval: 7,
  allowAnonymous: false
})

const notificationSettings = ref({
  emailEnabled: true,
  smsEnabled: false,
  systemEnabled: true,
  frequency: 'immediate'
})

const securitySettings = ref({
  passwordPolicy: ['minLength', 'requireNumber'],
  sessionTimeout: 120,
  maxLoginAttempts: 5,
  twoFactorAuth: false
})

const backupSettings = ref({
  autoBackup: true,
  frequency: 'daily',
  retentionDays: 30,
  backupPath: '/backup/evaluation-system'
})

// 方法
const handleMenuSelect = (key) => {
  activeTab.value = key
}

const saveBasicSettings = () => {
  ElMessage.success('基础设置保存成功')
}

const saveEvaluationSettings = () => {
  ElMessage.success('评估设置保存成功')
}

const saveNotificationSettings = () => {
  ElMessage.success('通知设置保存成功')
}

const saveSecuritySettings = () => {
  ElMessage.success('安全设置保存成功')
}

const saveBackupSettings = () => {
  ElMessage.success('备份设置保存成功')
}

const createBackup = async () => {
  backupLoading.value = true
  try {
    // 模拟备份过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    ElMessage.success('备份创建成功')
  } catch (error) {
    ElMessage.error('备份创建失败')
  } finally {
    backupLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.settings-page {
  .page-header {
    margin-bottom: @spacing-xl;
    
    h1 {
      margin: 0;
      color: @text-color-primary;
    }
  }
  
  .settings-menu {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;

    .el-menu {
      border: none;
    }
  }

  .settings-content {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    
    .setting-panel {
      h3 {
        margin: 0 0 @spacing-lg 0;
        color: @text-color-primary;
        border-bottom: 1px solid @border-color-lighter;
        padding-bottom: @spacing-md;
      }
    }
  }
}
</style>
