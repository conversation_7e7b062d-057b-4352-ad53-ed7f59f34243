<template>
  <div class="dashboard">
    <div class="page-header">
      <h1>仪表盘</h1>
      <p>欢迎使用评估分析系统，这里是系统概览</p>
    </div>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon primary">
            <el-icon><Document /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">156</div>
            <div class="stat-label">总评估数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon success">
            <el-icon><User /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">2340</div>
            <div class="stat-label">参与人数</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon warning">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">4.2</div>
            <div class="stat-label">平均评分</div>
          </div>
        </div>
      </el-col>
      
      <el-col :span="6">
        <div class="stat-card">
          <div class="stat-icon info">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">87%</div>
            <div class="stat-label">完成率</div>
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 最近活动 -->
    <div class="recent-activities">
      <div class="card-header">
        <h3>最近活动</h3>
        <el-button type="primary" size="small" @click="$router.push('/evaluation')">
          查看全部
        </el-button>
      </div>
      
      <el-table :data="recentEvaluations" style="width: 100%">
        <el-table-column prop="title" label="评估标题" />
        <el-table-column prop="type" label="类型">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="participants" label="参与人数" />
        <el-table-column prop="completedCount" label="已完成" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document, User, TrendCharts, DataAnalysis } from '@element-plus/icons-vue'

// 模拟数据
const recentEvaluations = ref([
  {
    id: 1,
    title: '员工绩效评估',
    type: 'performance',
    status: 'active',
    createTime: '2024-01-15',
    participants: 25,
    completedCount: 18
  },
  {
    id: 2,
    title: '客户满意度调查',
    type: 'satisfaction',
    status: 'completed',
    createTime: '2024-01-10',
    participants: 150,
    completedCount: 150
  },
  {
    id: 3,
    title: '培训效果评估',
    type: 'training',
    status: 'draft',
    createTime: '2024-01-20',
    participants: 0,
    completedCount: 0
  }
])

// 工具方法
const getTypeLabel = (type) => {
  const labels = {
    performance: '绩效评估',
    satisfaction: '满意度调查',
    training: '培训评估'
  }
  return labels[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    performance: 'primary',
    satisfaction: 'success',
    training: 'warning'
  }
  return types[type] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    active: '进行中',
    completed: '已完成',
    draft: '草稿'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    completed: 'info',
    draft: 'warning'
  }
  return types[status] || 'info'
}
</script>

<style lang="less" scoped>
.dashboard {
  .page-header {
    margin-bottom: 32px;
    
    h1 {
      margin: 0 0 8px 0;
      color: #303133;
    }
    
    p {
      margin: 0;
      color: #909399;
    }
  }
  
  .stats-row {
    margin-bottom: 32px;
    
    .stat-card {
      background: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
      
      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: #fff;
        
        &.primary { background-color: #409EFF; }
        &.success { background-color: #67C23A; }
        &.warning { background-color: #E6A23C; }
        &.info { background-color: #909399; }
      }
      
      .stat-content {
        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }
  
  .recent-activities {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    padding: 24px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
    }
  }
}
</style>
