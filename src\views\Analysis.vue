<template>
  <div class="analysis-page">
    <div class="page-header">
      <h1>数据分析</h1>
      <div class="header-actions">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleDateChange"
        />
        <el-button type="primary" @click="exportReport" :loading="analysisStore.loading">
          <el-icon><Download /></el-icon>
          导出报告
        </el-button>
      </div>
    </div>
    
    <!-- 概览统计 -->
    <el-row :gutter="20" class="overview-stats">
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.totalEvaluations || 0 }}</div>
          <div class="stat-label">总评估数</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.activeEvaluations || 0 }}</div>
          <div class="stat-label">进行中</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.completedEvaluations || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.totalParticipants || 0 }}</div>
          <div class="stat-label">总参与人数</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.averageScore || 0 }}</div>
          <div class="stat-label">平均评分</div>
        </div>
      </el-col>
      <el-col :span="4">
        <div class="stat-item">
          <div class="stat-value">{{ overviewStats.completionRate || 0 }}%</div>
          <div class="stat-label">完成率</div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 图表分析 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 趋势分析 -->
      <el-col :span="24">
        <div class="chart-card">
          <div class="card-header">
            <h3>评估趋势分析</h3>
            <el-radio-group v-model="trendType" @change="updateTrendChart">
              <el-radio-button label="monthly">月度</el-radio-button>
              <el-radio-button label="weekly">周度</el-radio-button>
            </el-radio-group>
          </div>
          <div class="chart-container">
            <v-chart 
              v-if="trendChartOption" 
              :option="trendChartOption" 
              style="height: 400px;"
            />
          </div>
        </div>
      </el-col>
      
      <!-- 分类分布和评分分布 -->
      <el-col :span="12">
        <div class="chart-card">
          <div class="card-header">
            <h3>评估类型分布</h3>
          </div>
          <div class="chart-container">
            <v-chart 
              v-if="categoryChartOption" 
              :option="categoryChartOption" 
              style="height: 350px;"
            />
          </div>
        </div>
      </el-col>
      
      <el-col :span="12">
        <div class="chart-card">
          <div class="card-header">
            <h3>评分分布</h3>
          </div>
          <div class="chart-container">
            <v-chart 
              v-if="scoreChartOption" 
              :option="scoreChartOption" 
              style="height: 350px;"
            />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <!-- 详细数据表格 -->
    <div class="data-table-section">
      <div class="card-header">
        <h3>详细数据</h3>
        <el-button @click="refreshData" :loading="analysisStore.loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
      </div>
      
      <el-table :data="categoryData" style="width: 100%">
        <el-table-column prop="name" label="评估类型" />
        <el-table-column prop="count" label="数量" />
        <el-table-column prop="percentage" label="占比">
          <template #default="{ row }">
            {{ row.percentage }}%
          </template>
        </el-table-column>
        <el-table-column label="趋势">
          <template #default="{ row }">
            <el-tag type="success" v-if="row.trend === 'up'">
              <el-icon><TrendCharts /></el-icon>
              上升
            </el-tag>
            <el-tag type="danger" v-else-if="row.trend === 'down'">
              <el-icon><TrendCharts /></el-icon>
              下降
            </el-tag>
            <el-tag type="info" v-else>
              <el-icon><TrendCharts /></el-icon>
              平稳
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import { Download, Refresh, TrendCharts } from '@element-plus/icons-vue'
import { useAnalysisStore } from '@/stores/analysis'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const analysisStore = useAnalysisStore()

// 响应式数据
const dateRange = ref([])
const trendType = ref('monthly')

// 计算属性
const overviewStats = computed(() => analysisStore.overviewStats)
const categoryData = computed(() => analysisStore.categoryData)

// 趋势图配置
const trendChartOption = computed(() => {
  const trendData = analysisStore.trendData
  if (!trendData.months.length) return null
  
  return {
    tooltip: { 
      trigger: 'axis',
      axisPointer: { type: 'cross' }
    },
    legend: { 
      data: ['评估数量', '参与人数', '平均评分'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: { 
      type: 'category', 
      data: trendData.months,
      boundaryGap: false
    },
    yAxis: [
      {
        type: 'value',
        name: '数量',
        position: 'left'
      },
      {
        type: 'value',
        name: '评分',
        position: 'right',
        min: 0,
        max: 5
      }
    ],
    series: [
      {
        name: '评估数量',
        type: 'line',
        data: trendData.evaluations,
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '参与人数',
        type: 'bar',
        data: trendData.participants,
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '平均评分',
        type: 'line',
        yAxisIndex: 1,
        data: trendData.avgScores,
        smooth: true,
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
})

// 分类饼图配置
const categoryChartOption = computed(() => {
  const data = analysisStore.categoryData
  if (!data.length) return null
  
  return {
    tooltip: { 
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [{
      name: '评估类型',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: { show: false },
      data: data.map(item => ({
        name: item.name,
        value: item.count
      }))
    }]
  }
})

// 评分分布柱状图配置
const scoreChartOption = computed(() => {
  const data = analysisStore.scoreDistribution
  if (!data.length) return null
  
  return {
    tooltip: { trigger: 'axis' },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.map(item => item.range)
    },
    yAxis: { type: 'value' },
    series: [{
      name: '人数',
      type: 'bar',
      data: data.map(item => item.count),
      itemStyle: {
        color: '#409EFF'
      }
    }]
  }
})

// 方法
const handleDateChange = (dates) => {
  if (dates && dates.length === 2) {
    refreshData()
  }
}

const updateTrendChart = () => {
  // 根据选择的时间类型更新图表
  refreshData()
}

const refreshData = async () => {
  try {
    await analysisStore.fetchAnalysisData(dateRange.value)
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

const exportReport = async () => {
  try {
    await analysisStore.exportAnalysisReport('pdf')
    ElMessage.success('报告导出成功')
  } catch (error) {
    ElMessage.error('报告导出失败')
  }
}

onMounted(() => {
  analysisStore.fetchAnalysisData()
})
</script>

<style lang="less" scoped>
.analysis-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-xl;
    
    h1 {
      margin: 0;
      color: @text-color-primary;
    }
    
    .header-actions {
      display: flex;
      gap: @spacing-md;
      align-items: center;
    }
  }
  
  .overview-stats {
    margin-bottom: @spacing-xl;
    
    .stat-item {
      background: #fff;
      border-radius: @border-radius-base;
      box-shadow: @box-shadow-base;
      padding: @spacing-lg;
      text-align: center;
      
      .stat-value {
        font-size: 32px;
        font-weight: bold;
        color: @primary-color;
        line-height: 1;
      }
      
      .stat-label {
        font-size: @font-size-sm;
        color: @text-color-secondary;
        margin-top: @spacing-sm;
      }
    }
  }
  
  .charts-section {
    margin-bottom: @spacing-xl;
    
    .chart-card {
      background: #fff;
      border-radius: @border-radius-base;
      box-shadow: @box-shadow-base;
      padding: @spacing-lg;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: @spacing-md;
        padding-bottom: @spacing-md;
        border-bottom: 1px solid @border-color-lighter;
        
        h3 {
          margin: 0;
          color: @text-color-primary;
        }
      }
      
      .chart-container {
        min-height: 300px;
      }
    }
  }
  
  .data-table-section {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: @spacing-md;
      
      h3 {
        margin: 0;
        color: @text-color-primary;
      }
    }
  }
}
</style>
