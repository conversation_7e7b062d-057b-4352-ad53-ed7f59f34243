<template>
  <div class="evaluation-page">
    <div class="page-header">
      <h1>评估管理</h1>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog = true">
          <el-icon><Plus /></el-icon>
          创建评估
        </el-button>
      </div>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchQuery"
            placeholder="搜索评估标题..."
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="草稿" value="draft" />
            <el-option label="进行中" value="active" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="typeFilter" placeholder="类型筛选" clearable @change="handleFilter">
            <el-option label="全部" value="" />
            <el-option label="绩效评估" value="performance" />
            <el-option label="满意度调查" value="satisfaction" />
            <el-option label="培训评估" value="training" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 评估列表 -->
    <div class="evaluation-list">
      <el-table 
        :data="filteredEvaluations" 
        v-loading="evaluationStore.loading"
        style="width: 100%"
      >
        <el-table-column prop="title" label="评估标题" min-width="200">
          <template #default="{ row }">
            <div class="evaluation-title">
              <strong>{{ row.title }}</strong>
              <p class="description">{{ row.description }}</p>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)">{{ getTypeLabel(row.type) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">{{ getStatusLabel(row.status) }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="participants" label="参与人数" width="100" />
        <el-table-column prop="completedCount" label="已完成" width="100" />
        
        <el-table-column label="完成率" width="120">
          <template #default="{ row }">
            <el-progress 
              :percentage="row.participants > 0 ? Math.round((row.completedCount / row.participants) * 100) : 0"
              :stroke-width="6"
              :show-text="false"
            />
            <span class="progress-text">
              {{ row.participants > 0 ? Math.round((row.completedCount / row.participants) * 100) : 0 }}%
            </span>
          </template>
        </el-table-column>
        
        <el-table-column prop="createTime" label="创建时间" width="120" />
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewEvaluation(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editEvaluation(row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deleteEvaluation(row)"
              :disabled="row.status === 'active'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 创建/编辑评估对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingEvaluation ? '编辑评估' : '创建评估'"
      width="600px"
      @close="resetForm"
    >
      <el-form :model="evaluationForm" :rules="formRules" ref="formRef" label-width="100px">
        <el-form-item label="评估标题" prop="title">
          <el-input v-model="evaluationForm.title" placeholder="请输入评估标题" />
        </el-form-item>
        
        <el-form-item label="评估类型" prop="type">
          <el-select v-model="evaluationForm.type" placeholder="请选择评估类型">
            <el-option label="绩效评估" value="performance" />
            <el-option label="满意度调查" value="satisfaction" />
            <el-option label="培训评估" value="training" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="evaluationForm.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入评估描述"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="evaluationStore.loading">
          {{ editingEvaluation ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { useEvaluationStore } from '@/stores/evaluation'

const evaluationStore = useEvaluationStore()

// 响应式数据
const searchQuery = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const showCreateDialog = ref(false)
const editingEvaluation = ref(null)
const formRef = ref()

// 表单数据
const evaluationForm = ref({
  title: '',
  type: '',
  description: ''
})

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入评估标题', trigger: 'blur' }],
  type: [{ required: true, message: '请选择评估类型', trigger: 'change' }],
  description: [{ required: true, message: '请输入评估描述', trigger: 'blur' }]
}

// 计算属性
const filteredEvaluations = computed(() => {
  let result = evaluationStore.evaluations
  
  // 搜索过滤
  if (searchQuery.value) {
    result = result.filter(item => 
      item.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      item.description.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }
  
  // 状态过滤
  if (statusFilter.value) {
    result = result.filter(item => item.status === statusFilter.value)
  }
  
  // 类型过滤
  if (typeFilter.value) {
    result = result.filter(item => item.type === typeFilter.value)
  }
  
  return result
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const handleFilter = () => {
  // 筛选逻辑已在计算属性中处理
}

const resetFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  typeFilter.value = ''
}

const viewEvaluation = (evaluation) => {
  evaluationStore.setCurrentEvaluation(evaluation)
  // 这里可以跳转到详情页面或打开详情对话框
  ElMessage.info('查看功能待实现')
}

const editEvaluation = (evaluation) => {
  editingEvaluation.value = evaluation
  evaluationForm.value = {
    title: evaluation.title,
    type: evaluation.type,
    description: evaluation.description
  }
  showCreateDialog.value = true
}

const deleteEvaluation = async (evaluation) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除评估"${evaluation.title}"吗？`,
      '确认删除',
      { type: 'warning' }
    )
    
    await evaluationStore.deleteEvaluation(evaluation.id)
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    
    if (editingEvaluation.value) {
      await evaluationStore.updateEvaluation(editingEvaluation.value.id, evaluationForm.value)
      ElMessage.success('更新成功')
    } else {
      await evaluationStore.createEvaluation(evaluationForm.value)
      ElMessage.success('创建成功')
    }
    
    showCreateDialog.value = false
    resetForm()
  } catch (error) {
    console.error('提交失败:', error)
  }
}

const resetForm = () => {
  editingEvaluation.value = null
  evaluationForm.value = {
    title: '',
    type: '',
    description: ''
  }
  formRef.value?.resetFields()
}

// 工具方法
const getTypeLabel = (type) => {
  const labels = {
    performance: '绩效评估',
    satisfaction: '满意度调查',
    training: '培训评估'
  }
  return labels[type] || type
}

const getTypeTagType = (type) => {
  const types = {
    performance: 'primary',
    satisfaction: 'success',
    training: 'warning'
  }
  return types[type] || 'info'
}

const getStatusLabel = (status) => {
  const labels = {
    active: '进行中',
    completed: '已完成',
    draft: '草稿'
  }
  return labels[status] || status
}

const getStatusTagType = (status) => {
  const types = {
    active: 'success',
    completed: 'info',
    draft: 'warning'
  }
  return types[status] || 'info'
}

onMounted(() => {
  evaluationStore.fetchEvaluations()
})
</script>

<style lang="less" scoped>
.evaluation-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: @spacing-xl;
    
    h1 {
      margin: 0;
      color: @text-color-primary;
    }
  }
  
  .search-section {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    margin-bottom: @spacing-lg;
  }

  .evaluation-list {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    padding: @spacing-lg;
    
    .evaluation-title {
      .description {
        margin: @spacing-xs 0 0 0;
        font-size: @font-size-sm;
        color: @text-color-secondary;
        .text-ellipsis();
      }
    }
    
    .progress-text {
      margin-left: @spacing-xs;
      font-size: @font-size-sm;
      color: @text-color-secondary;
    }
  }
}
</style>
